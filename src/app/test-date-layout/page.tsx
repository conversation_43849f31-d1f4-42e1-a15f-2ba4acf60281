"use client";

import { SimpleDateRange } from "@/components/ui/simple-date-range";
import { useState } from "react";

export default function TestDateLayoutPage() {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">日期选择器布局测试</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h2 className="text-xl font-semibold mb-4">SimpleDateRange 组件测试</h2>
        <p className="text-gray-600 mb-6">
          测试日期选择器的两个按钮是否在同一行水平排列：
        </p>
        
        <div className="space-y-6">
          {/* 测试1：基本布局 */}
          <div>
            <h3 className="text-lg font-medium mb-2">测试1：基本布局</h3>
            <div className="border p-4 rounded">
              <SimpleDateRange
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={setStartDate}
                onEndDateChange={setEndDate}
                placeholder="选择日期范围"
              />
            </div>
          </div>

          {/* 测试2：在 flex 容器中 */}
          <div>
            <h3 className="text-lg font-medium mb-2">测试2：在 flex 容器中（模拟高级搜索）</h3>
            <div className="border p-4 rounded">
              <div className="flex flex-col lg:flex-row lg:items-end gap-3">
                <div className="w-full lg:w-48">
                  <label className="block text-sm font-medium mb-1">字段</label>
                  <select className="w-full h-9 px-3 border border-input rounded-md">
                    <option>创建日期</option>
                  </select>
                </div>
                <div className="w-full lg:w-48">
                  <label className="block text-sm font-medium mb-1">值</label>
                  <SimpleDateRange
                    startDate={startDate}
                    endDate={endDate}
                    onStartDateChange={setStartDate}
                    onEndDateChange={setEndDate}
                    placeholder="选择日期范围"
                    className="h-9"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 测试3：窄容器中 */}
          <div>
            <h3 className="text-lg font-medium mb-2">测试3：窄容器中</h3>
            <div className="border p-4 rounded max-w-md">
              <SimpleDateRange
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={setStartDate}
                onEndDateChange={setEndDate}
                placeholder="选择日期范围"
              />
            </div>
          </div>
        </div>

        {/* 显示当前选择的日期 */}
        {(startDate || endDate) && (
          <div className="mt-6 p-4 bg-gray-50 rounded">
            <h4 className="font-medium mb-2">当前选择：</h4>
            <p>开始日期: {startDate || '未选择'}</p>
            <p>结束日期: {endDate || '未选择'}</p>
          </div>
        )}
      </div>

      <div className="bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-blue-800">预期效果</h3>
        <ul className="list-disc list-inside space-y-2 text-blue-700">
          <li>两个日期按钮应该在同一行水平排列</li>
          <li>每个按钮宽度为 w-40 (10rem)</li>
          <li>按钮之间有 gap-2 的间距</li>
          <li>点击按钮应该打开原生浏览器日期选择器</li>
          <li>在窄屏幕上应该保持水平布局</li>
        </ul>
      </div>
    </div>
  );
}
