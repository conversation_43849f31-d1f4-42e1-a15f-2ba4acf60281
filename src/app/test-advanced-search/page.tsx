"use client";

import { useState } from "react";
import AdvancedSearchRefactored from "@/components/AdvancedSearchRefactored";
import { SearchCondition } from "@/lib/api";
import { DatabaseFieldConfig } from "@/lib/configCache";

// Mock field configurations for testing
const mockFields: DatabaseFieldConfig[] = [
  {
    fieldName: "title",
    displayName: "Title",
    fieldType: "string",
    filterType: "contains",
    searchType: "contains",
    isAdvancedSearchable: true,
    isFilterable: true,
    isSearchable: true
  },
  {
    fieldName: "category",
    displayName: "Category",
    fieldType: "string",
    filterType: "multi_select",
    searchType: "exact",
    isAdvancedSearchable: true,
    isFilterable: true,
    isSearchable: true
  },
  {
    fieldName: "status",
    displayName: "Status",
    fieldType: "string",
    filterType: "select",
    searchType: "exact",
    isAdvancedSearchable: true,
    isFilterable: true,
    isSearchable: true
  },
  {
    fieldName: "created_date",
    displayName: "Created Date",
    fieldType: "date",
    filterType: "date_range",
    searchType: "date_range",
    isAdvancedSearchable: true,
    isFilterable: true,
    isSearchable: true
  },
  {
    fieldName: "price",
    displayName: "Price",
    fieldType: "number",
    filterType: "range",
    searchType: "range",
    isAdvancedSearchable: true,
    isFilterable: true,
    isSearchable: true
  },
  {
    fieldName: "is_active",
    displayName: "Is Active",
    fieldType: "boolean",
    filterType: "select",
    searchType: "exact",
    isAdvancedSearchable: true,
    isFilterable: true,
    isSearchable: true
  }
];

// Mock metadata for select/multi-select fields
const mockMetadata = {
  category: ["Electronics", "Books", "Clothing", "Home & Garden", "Sports"],
  status: ["Active", "Inactive", "Pending", "Archived"]
};

export default function TestAdvancedSearchPage() {
  const [searchResults, setSearchResults] = useState<SearchCondition[]>([]);

  const handleSearch = (conditions: SearchCondition[]) => {
    console.log("Search conditions:", conditions);
    setSearchResults(conditions);
  };

  const handleClear = () => {
    console.log("Clear search");
    setSearchResults([]);
  };

  return (
    <div className="container mx-auto p-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-8">Advanced Search Alignment Test</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h2 className="text-xl font-semibold mb-4">Test Advanced Search Component</h2>
        <p className="text-gray-600 mb-6">
          This page tests the alignment fixes for the advanced search interface. 
          Click the "Advanced Search" button below to test:
        </p>
        <ul className="list-disc list-inside text-gray-600 mb-6 space-y-1">
          <li>Horizontal alignment of field dropdowns and date pickers</li>
          <li>Consistent component heights (h-9)</li>
          <li>Proper spacing and responsive behavior</li>
          <li>Native browser date picker functionality</li>
          <li>Multi-select and range input alignment</li>
        </ul>
        
        <div className="flex gap-4 items-center">
          <AdvancedSearchRefactored
            onSearch={handleSearch}
            onClear={handleClear}
            availableFields={mockFields}
            metadata={mockMetadata}
            currentConditions={[]}
            currentFilters={{}}
            showFilterIntegration={true}
          />
          <span className="text-sm text-gray-500">
            ← Click to test alignment fixes
          </span>
        </div>
      </div>

      {searchResults.length > 0 && (
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Search Results</h3>
          <pre className="bg-white p-4 rounded border text-sm overflow-auto">
            {JSON.stringify(searchResults, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-8 bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-blue-800">Testing Instructions</h3>
        <ol className="list-decimal list-inside space-y-2 text-blue-700">
          <li>Click the "Advanced Search" button to open the dialog</li>
          <li>Add multiple search conditions using "Add Condition"</li>
          <li>Test different field types: text, select, multi-select, date range, number range, boolean</li>
          <li>Verify that all components align horizontally on the same line</li>
          <li>Test date pickers click to open native browser calendars</li>
          <li>Resize the browser window to test responsive behavior</li>
          <li>Verify that components stack vertically on mobile screens</li>
        </ol>
      </div>
    </div>
  );
}
