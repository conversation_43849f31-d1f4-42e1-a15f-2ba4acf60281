"use client";

import { SimpleDateRange } from "@/components/ui/simple-date-range";
import { useState } from "react";

export default function TestTextWrappingPage() {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  return (
    <div className="container mx-auto p-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-8">Text Wrapping Fix Test</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h2 className="text-xl font-semibold mb-4">Date Picker Button Text Wrapping Fix</h2>
        <p className="text-gray-600 mb-6">
          This page tests the fix for text wrapping in date picker buttons:
        </p>
        <ul className="list-disc list-inside text-gray-600 mb-6 space-y-1">
          <li><strong>Before:</strong> "Start Date" and "End Date" text was wrapping to multiple lines</li>
          <li><strong>After:</strong> Text should display on single lines without wrapping</li>
          <li><strong>Button Width:</strong> Increased from w-40 to responsive widths (w-32 to w-44)</li>
          <li><strong>Container Width:</strong> Increased from lg:w-48 to lg:w-80 in AdvancedSearch</li>
        </ul>
      </div>

      <div className="space-y-8">
        {/* Test 1: Basic Layout */}
        <div className="bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 1: Basic Layout - Text Wrapping Check</h3>
          <p className="text-sm text-gray-600 mb-4">
            The "Start Date" and "End Date" text should be on single lines:
          </p>
          <SimpleDateRange
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
          />
        </div>

        {/* Test 2: Narrow Container */}
        <div className="bg-green-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 2: Narrow Container (300px width)</h3>
          <p className="text-sm text-gray-600 mb-4">
            Test responsive behavior in a narrow container:
          </p>
          <div className="w-80 border-2 border-dashed border-green-300 p-4">
            <SimpleDateRange
              startDate={startDate}
              endDate={endDate}
              onStartDateChange={setStartDate}
              onEndDateChange={setEndDate}
            />
          </div>
        </div>

        {/* Test 3: Advanced Search Simulation */}
        <div className="bg-yellow-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 3: Advanced Search Layout Simulation</h3>
          <p className="text-sm text-gray-600 mb-4">
            This simulates the exact layout used in AdvancedSearchRefactored with the new lg:w-80 container:
          </p>
          <div className="flex flex-col lg:flex-row lg:items-end gap-3">
            <div className="w-full lg:w-48">
              <label className="block text-sm font-medium mb-1">Field</label>
              <select className="w-full h-9 px-3 border border-input rounded-md bg-background">
                <option>Created Date</option>
              </select>
            </div>
            <div className="w-full lg:w-80">
              <label className="block text-sm font-medium mb-1">Value</label>
              <SimpleDateRange
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={setStartDate}
                onEndDateChange={setEndDate}
                className="h-9"
              />
            </div>
          </div>
        </div>

        {/* Test 4: Mobile Responsive */}
        <div className="bg-purple-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 4: Mobile Responsive (Simulated)</h3>
          <p className="text-sm text-gray-600 mb-4">
            Test how the component behaves on mobile screens (simulated with max-width):
          </p>
          <div className="max-w-sm border-2 border-dashed border-purple-300 p-4">
            <SimpleDateRange
              startDate={startDate}
              endDate={endDate}
              onStartDateChange={setStartDate}
              onEndDateChange={setEndDate}
            />
          </div>
        </div>

        {/* Test 5: With Selected Dates */}
        <div className="bg-red-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 5: With Selected Dates (Longer Text)</h3>
          <p className="text-sm text-gray-600 mb-4">
            Test with actual dates selected to see how longer formatted text displays:
          </p>
          <SimpleDateRange
            startDate="2024-01-15"
            endDate="2024-12-31"
            onStartDateChange={() => {}}
            onEndDateChange={() => {}}
          />
          <p className="text-sm text-gray-500 mt-2">
            Should display: "Jan 15, 2024" and "Dec 31, 2024" without wrapping
          </p>
        </div>
      </div>

      <div className="mt-8 bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Visual Inspection Checklist</h3>
        <div className="space-y-2 text-gray-700">
          <div className="flex items-center gap-2">
            <input type="checkbox" id="single-line" />
            <label htmlFor="single-line">✓ "Start Date" and "End Date" text displays on single lines</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" id="no-wrapping" />
            <label htmlFor="no-wrapping">✓ No text wrapping occurs in any of the test scenarios</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" id="responsive" />
            <label htmlFor="responsive">✓ Buttons scale appropriately across different screen sizes</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" id="formatted-dates" />
            <label htmlFor="formatted-dates">✓ Formatted dates (e.g., "Jan 15, 2024") display without wrapping</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" id="alignment" />
            <label htmlFor="alignment">✓ Buttons maintain proper horizontal alignment</label>
          </div>
        </div>
      </div>
    </div>
  );
}
