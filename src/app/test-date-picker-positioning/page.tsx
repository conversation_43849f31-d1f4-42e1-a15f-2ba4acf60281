"use client";

import { SimpleDateRange } from "@/components/ui/simple-date-range";
import { useState } from "react";

export default function TestDatePickerPositioningPage() {
  const [startDate1, setStartDate1] = useState("");
  const [endDate1, setEndDate1] = useState("");
  const [startDate2, setStartDate2] = useState("");
  const [endDate2, setEndDate2] = useState("");
  const [startDate3, setStartDate3] = useState("");
  const [endDate3, setEndDate3] = useState("");

  return (
    <div className="container mx-auto p-8 max-w-6xl">
      <h1 className="text-3xl font-bold mb-8">Date Picker Positioning Test</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h2 className="text-xl font-semibold mb-4">Critical Issues Testing</h2>
        <p className="text-gray-600 mb-6">
          This page tests the fixes for the SimpleDateRange component critical issues:
        </p>
        <ul className="list-disc list-inside text-gray-600 mb-6 space-y-1">
          <li><strong>Height Consistency:</strong> Both buttons should maintain h-9 height</li>
          <li><strong>Popup Positioning:</strong> Date picker should appear near the clicked button</li>
          <li><strong>Horizontal Layout:</strong> Both buttons should be on the same row</li>
          <li><strong>Clean Implementation:</strong> No problematic CSS positioning</li>
        </ul>
      </div>

      <div className="space-y-8">
        {/* Test 1: Top of page */}
        <div className="bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 1: Top of Page</h3>
          <p className="text-sm text-gray-600 mb-4">
            Click the date buttons below. The native date picker should appear near the button, not in screen corners.
          </p>
          <SimpleDateRange
            startDate={startDate1}
            endDate={endDate1}
            onStartDateChange={setStartDate1}
            onEndDateChange={setEndDate1}
          />
          {(startDate1 || endDate1) && (
            <div className="mt-2 text-sm text-blue-700">
              Selected: {startDate1 || 'None'} to {endDate1 || 'None'}
            </div>
          )}
        </div>

        {/* Test 2: Middle of page in flex container */}
        <div className="bg-green-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 2: In Flex Container (Advanced Search Simulation)</h3>
          <p className="text-sm text-gray-600 mb-4">
            This simulates the exact layout used in AdvancedSearchRefactored component.
          </p>
          <div className="flex flex-col lg:flex-row lg:items-end gap-3">
            <div className="w-full lg:w-48">
              <label className="block text-sm font-medium mb-1">Field</label>
              <select className="w-full h-9 px-3 border border-input rounded-md bg-background">
                <option>Created Date</option>
                <option>Updated Date</option>
                <option>Expiry Date</option>
              </select>
            </div>
            <div className="w-full lg:w-48">
              <label className="block text-sm font-medium mb-1">Value</label>
              <SimpleDateRange
                startDate={startDate2}
                endDate={endDate2}
                onStartDateChange={setStartDate2}
                onEndDateChange={setEndDate2}
                className="h-9"
              />
            </div>
          </div>
          {(startDate2 || endDate2) && (
            <div className="mt-2 text-sm text-green-700">
              Selected: {startDate2 || 'None'} to {endDate2 || 'None'}
            </div>
          )}
        </div>

        {/* Test 3: Bottom of page with scroll */}
        <div className="bg-yellow-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 3: Bottom of Page</h3>
          <p className="text-sm text-gray-600 mb-4">
            Test positioning when the component is at the bottom of the page.
          </p>
          <SimpleDateRange
            startDate={startDate3}
            endDate={endDate3}
            onStartDateChange={setStartDate3}
            onEndDateChange={setEndDate3}
          />
          {(startDate3 || endDate3) && (
            <div className="mt-2 text-sm text-yellow-700">
              Selected: {startDate3 || 'None'} to {endDate3 || 'None'}
            </div>
          )}
        </div>

        {/* Height comparison test */}
        <div className="bg-purple-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test 4: Height Consistency Check</h3>
          <p className="text-sm text-gray-600 mb-4">
            All components below should have the same height (h-9 = 36px):
          </p>
          <div className="flex gap-4 items-center">
            <input 
              type="text" 
              placeholder="Regular input" 
              className="h-9 px-3 border border-input rounded-md bg-background"
            />
            <select className="h-9 px-3 border border-input rounded-md bg-background">
              <option>Select option</option>
            </select>
            <SimpleDateRange
              startDate=""
              endDate=""
              onStartDateChange={() => {}}
              onEndDateChange={() => {}}
            />
          </div>
        </div>
      </div>

      <div className="mt-8 bg-red-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4 text-red-800">Testing Checklist</h3>
        <div className="space-y-2 text-red-700">
          <div className="flex items-center gap-2">
            <input type="checkbox" id="height" />
            <label htmlFor="height">✓ Both Start Date and End Date buttons have consistent h-9 height</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" id="horizontal" />
            <label htmlFor="horizontal">✓ Both buttons are horizontally aligned on the same row</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" id="positioning" />
            <label htmlFor="positioning">✓ Date picker popup appears near the clicked button (not in screen corners)</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" id="functionality" />
            <label htmlFor="functionality">✓ Click functionality works for both Start Date and End Date</label>
          </div>
          <div className="flex items-center gap-2">
            <input type="checkbox" id="responsive" />
            <label htmlFor="responsive">✓ Component works correctly across different screen sizes</label>
          </div>
        </div>
      </div>
    </div>
  );
}
