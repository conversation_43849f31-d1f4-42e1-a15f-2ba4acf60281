"use client";

import { Calendar } from "lucide-react";
import { useRef } from "react";

interface SimpleDateRangeProps {
  startDate?: string;
  endDate?: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  placeholder?: string;
  className?: string;
}

export function SimpleDateRange({
  startDate = "",
  endDate = "",
  onStartDateChange,
  onEndDateChange,
  placeholder = "Select date range",
  className = ""
}: SimpleDateRangeProps) {
  const startDateRef = useRef<HTMLInputElement>(null);
  const endDateRef = useRef<HTMLInputElement>(null);

  const formatDisplayDate = (dateStr: string, fieldType: 'start' | 'end') => {
    if (!dateStr) {
      return fieldType === 'start' ? "Start Date" : "End Date";
    }
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  const handleStartDateClick = () => {
    if (startDateRef.current) {
      startDateRef.current.focus();
      startDateRef.current.showPicker?.();
    }
  };

  const handleEndDateClick = () => {
    if (endDateRef.current) {
      endDateRef.current.focus();
      endDateRef.current.showPicker?.();
    }
  };

  return (
    <div className={`flex gap-2 w-full ${className || ''}`}>
      {/* Start Date */}
      <div className="relative w-32 sm:w-36 md:w-40 lg:w-44">
        <input
          ref={startDateRef}
          type="date"
          value={startDate}
          onChange={(e) => onStartDateChange(e.target.value)}
          className="sr-only"
        />
        <button
          type="button"
          onClick={handleStartDateClick}
          className="flex items-center justify-start w-full h-9 px-3 py-1 text-sm font-normal text-left border border-input rounded-md bg-background shadow-sm hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors whitespace-nowrap"
        >
          <Calendar className="mr-2 h-4 w-4 text-muted-foreground flex-shrink-0" />
          <span className={`${startDate ? "text-foreground" : "text-muted-foreground"} truncate`}>
            {formatDisplayDate(startDate, 'start')}
          </span>
        </button>
      </div>

      {/* End Date */}
      <div className="relative w-32 sm:w-36 md:w-40 lg:w-44">
        <input
          ref={endDateRef}
          type="date"
          value={endDate}
          onChange={(e) => onEndDateChange(e.target.value)}
          className="sr-only"
        />
        <button
          type="button"
          onClick={handleEndDateClick}
          className="flex items-center justify-start w-full h-9 px-3 py-1 text-sm font-normal text-left border border-input rounded-md bg-background shadow-sm hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors whitespace-nowrap"
        >
          <Calendar className="mr-2 h-4 w-4 text-muted-foreground flex-shrink-0" />
          <span className={`${endDate ? "text-foreground" : "text-muted-foreground"} truncate`}>
            {formatDisplayDate(endDate, 'end')}
          </span>
        </button>
      </div>
    </div>
  );
}
