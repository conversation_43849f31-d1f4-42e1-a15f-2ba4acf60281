"use client";

import { Calendar } from "lucide-react";
import { useRef } from "react";

interface SimpleDateRangeProps {
  startDate?: string;
  endDate?: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  placeholder?: string;
  className?: string;
}

export function SimpleDateRange({
  startDate = "",
  endDate = "",
  onStartDateChange,
  onEndDateChange,
  placeholder = "Select date range",
  className = ""
}: SimpleDateRangeProps) {
  const startDateRef = useRef<HTMLInputElement>(null);
  const endDateRef = useRef<HTMLInputElement>(null);

  const formatDisplayDate = (dateStr: string, fieldType: 'start' | 'end') => {
    if (!dateStr) {
      return fieldType === 'start' ? "Start Date" : "End Date";
    }
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  const handleStartDateClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (startDateRef.current) {
      startDateRef.current.showPicker?.();
    }
  };

  const handleEndDateClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (endDateRef.current) {
      endDateRef.current.showPicker?.();
    }
  };

  return (
    <div className={`flex gap-2 w-full ${className || ''}`}>
      {/* Start Date */}
      <div className="relative w-40">
        {/* Hidden native date input - positioned absolutely to not take up space */}
        <input
          ref={startDateRef}
          type="date"
          value={startDate}
          onChange={(e) => {
            e.stopPropagation();
            onStartDateChange(e.target.value);
          }}
          onFocus={(e) => e.stopPropagation()}
          onBlur={(e) => e.stopPropagation()}
          className="absolute top-0 left-0 w-0 h-0 opacity-0 pointer-events-none overflow-hidden"
          tabIndex={-1}
        />
        {/* Visible button with integrated label */}
        <button
          type="button"
          onClick={handleStartDateClick}
          onMouseDown={(e) => e.stopPropagation()}
          className="flex items-center justify-start w-full h-9 px-3 py-1 text-sm font-normal text-left border border-input rounded-md bg-background shadow-sm hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
        >
          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className={startDate ? "text-foreground" : "text-muted-foreground"}>
            {formatDisplayDate(startDate, 'start')}
          </span>
        </button>
      </div>

      {/* End Date */}
      <div className="relative w-40">
        {/* Hidden native date input - positioned absolutely to not take up space */}
        <input
          ref={endDateRef}
          type="date"
          value={endDate}
          onChange={(e) => {
            e.stopPropagation();
            onEndDateChange(e.target.value);
          }}
          onFocus={(e) => e.stopPropagation()}
          onBlur={(e) => e.stopPropagation()}
          className="absolute top-0 left-0 w-0 h-0 opacity-0 pointer-events-none overflow-hidden"
          tabIndex={-1}
        />
        {/* Visible button with integrated label */}
        <button
          type="button"
          onClick={handleEndDateClick}
          onMouseDown={(e) => e.stopPropagation()}
          className="flex items-center justify-start w-full h-9 px-3 py-1 text-sm font-normal text-left border border-input rounded-md bg-background shadow-sm hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors"
        >
          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className={endDate ? "text-foreground" : "text-muted-foreground"}>
            {formatDisplayDate(endDate, 'end')}
          </span>
        </button>
      </div>

      {/* Display selected range */}
      {startDate && endDate && (
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded mt-2">
          Selected: {formatDisplayDate(startDate, 'start')} - {formatDisplayDate(endDate, 'end')}
        </div>
      )}
    </div>
  );
}
