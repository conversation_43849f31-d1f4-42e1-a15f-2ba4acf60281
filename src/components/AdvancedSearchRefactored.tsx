"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { SimpleDateRange } from "@/components/ui/simple-date-range";
import { MultiSelect } from "@/components/ui/multi-select";
import { Filter, Plus, X, Search, ArrowDown } from "lucide-react";
import { convertFiltersToConditions, mergeFilterAndAdvancedConditions } from '@/lib/filterToConditionConverter';
import { SearchCondition } from '@/lib/api';
import { DatabaseFieldConfig } from '@/lib/configCache';

interface AdvancedSearchProps {
  onSearch: (conditions: SearchCondition[]) => void;
  onClear: () => void;
  availableFields: DatabaseFieldConfig[];
  currentConditions?: SearchCondition[];
  metadata?: Record<string, string[]>;
  currentFilters?: Record<string, unknown>;
  showFilterIntegration?: boolean;
}

export default function AdvancedSearchRefactored({
  onSearch,
  onClear,
  availableFields,
  currentConditions = [],
  metadata = {},
  currentFilters = {},
  showFilterIntegration = true
}: AdvancedSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [conditions, setConditions] = useState<SearchCondition[]>(currentConditions);
  const [isDatePickerActive, setIsDatePickerActive] = useState(false);
  const [preventClose, setPreventClose] = useState(false);

  // Filter fields to only show those marked as advanced searchable
  const searchableFields = availableFields.filter(f => f.isAdvancedSearchable);

  // Check if there are active filters that can be imported
  const hasActiveFilters = Object.values(currentFilters).some(value => 
    value !== undefined && value !== null && value !== '' && 
    (!Array.isArray(value) || value.length > 0)
  );

  // Update conditions when currentConditions prop changes
  useEffect(() => {
    setConditions(currentConditions);
  }, [currentConditions]);

  // Simplified event handling - let Radix handle most of the modal behavior
  // Only prevent close during active date picker interactions

  const addCondition = () => {
    const newCondition: SearchCondition = {
      id: `condition_${Date.now()}`,
      field: searchableFields[0]?.fieldName || '',
      value: '',
      operator: 'contains',
      logic: conditions.length > 0 ? 'AND' : undefined
    };
    setConditions([...conditions, newCondition]);
  };

  const removeCondition = (id: string) => {
    setConditions(conditions.filter(c => c.id !== id));
  };

  const updateCondition = (id: string, updates: Partial<SearchCondition>) => {
    setConditions(conditions.map(c => 
      c.id === id ? { ...c, ...updates } : c
    ));
  };

  const handleSearch = () => {
    const validConditions = conditions.filter(c => {
      if (typeof c.value === 'string') {
        return c.value.trim() !== '';
      }
      if (Array.isArray(c.value)) {
        return c.value.length > 0;
      }
      if (typeof c.value === 'object' && c.value !== null) {
        const obj = c.value as { from?: string; to?: string };
        return obj.from || obj.to;
      }
      return c.value !== undefined && c.value !== null;
    });

    onSearch(validConditions);
    setIsOpen(false);
  };

  const handleClear = () => {
    setConditions([]);
    onClear();
    setIsOpen(false);
  };

  const handleImportFilters = () => {
    const filterConditions = convertFiltersToConditions(currentFilters, availableFields);
    const mergedConditions = mergeFilterAndAdvancedConditions(filterConditions, conditions);
    setConditions(mergedConditions);
  };

  // Render appropriate input component based on field's filterType and searchType
  const renderValueInput = (condition: SearchCondition, field: any) => {
    // First check filterType for UI component selection, then fallback to searchType
    const filterType = field.filterType;
    const searchType = field.searchType || field.fieldType;

    // Handle filterType-based component selection first
    if (filterType === 'multi_select') {
      const options = metadata[field.fieldName] || [];
      const validOptions = options.filter(option =>
        typeof option === 'string' && option.trim() !== ''
      );

      return (
        <MultiSelect
          options={validOptions.map(option => ({ value: option, label: option }))}
          value={Array.isArray(condition.value) ? condition.value.map(String) : []}
          onValueChange={(value) => updateCondition(condition.id, { value })}
          placeholder={`Select ${field.displayName}`}
        />
      );
    }

    if (filterType === 'select') {
      if (field.fieldType === 'boolean') {
        return (
          <Select
            value={typeof condition.value === "string" ? condition.value : '__none__'}
            onValueChange={(value) => updateCondition(condition.id, {
              value: value === "__none__" ? '' : value
            })}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select value" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="__none__">Select value</SelectItem>
              <SelectItem value="true">Yes</SelectItem>
              <SelectItem value="false">No</SelectItem>
            </SelectContent>
          </Select>
        );
      }

      const options = metadata[field.fieldName] || [];
      const validOptions = options.filter(option =>
        typeof option === 'string' && option.trim() !== ''
      );

      return (
        <Select
          value={typeof condition.value === "string" ? condition.value : '__all__'}
          onValueChange={(value) => updateCondition(condition.id, {
            value: value === "__all__" ? '' : value
          })}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={`Select ${field.displayName}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="__all__">All</SelectItem>
            {validOptions.map((option) => (
              <SelectItem key={option} value={option}>
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    if (filterType === 'date_range') {
      const dateValue = (typeof condition.value === 'object' && condition.value !== null && !Array.isArray(condition.value))
        ? condition.value as { from?: string; to?: string }
        : { from: '', to: '' };
      return (
        <SimpleDateRange
          startDate={dateValue.from || ''}
          endDate={dateValue.to || ''}
          onStartDateChange={(date) => updateCondition(condition.id, {
            value: { ...dateValue, from: date }
          })}
          onEndDateChange={(date) => updateCondition(condition.id, {
            value: { ...dateValue, to: date }
          })}
          placeholder={`Select ${field.displayName.toLowerCase()} range`}
          className="w-full"
        />
      );
    }

    if (filterType === 'range') {
      // Type guard to ensure we have a range object
      const isRangeObject = (value: any): value is { from?: string; to?: string } => {
        return typeof value === "object" && value !== null && !Array.isArray(value) && !(value instanceof Date);
      };

      const rangeValue = isRangeObject(condition.value) ? condition.value : { from: '', to: '' };
      return (
        <div className="flex gap-2 items-center">
          <Input
            type="number"
            placeholder="Min"
            value={rangeValue.from || ''}
            onChange={(e) => updateCondition(condition.id, {
              value: { ...rangeValue, from: e.target.value }
            })}
            className="flex-1"
          />
          <span className="text-gray-400">to</span>
          <Input
            type="number"
            placeholder="Max"
            value={rangeValue.to || ''}
            onChange={(e) => updateCondition(condition.id, {
              value: { ...rangeValue, to: e.target.value }
            })}
            className="flex-1"
          />
        </div>
      );
    }

    // Fallback to searchType-based logic for other cases
    switch (searchType) {
      case 'date_range':
        // Type guard to ensure we have a range object
        const isDateRangeObject = (value: any): value is { from?: string; to?: string } => {
          return typeof value === "object" && value !== null && !Array.isArray(value) && !(value instanceof Date);
        };

        const dateValue = isDateRangeObject(condition.value) ? condition.value : { from: '', to: '' };
        return (
          <SimpleDateRange
            startDate={dateValue.from || ''}
            endDate={dateValue.to || ''}
            onStartDateChange={(date) => updateCondition(condition.id, {
              value: { ...dateValue, from: date }
            })}
            onEndDateChange={(date) => updateCondition(condition.id, {
              value: { ...dateValue, to: date }
            })}
            placeholder={`Select ${field.displayName.toLowerCase()} range`}
            className="w-full"
          />
        );

      case 'date':
        return (
          <Input
            type="date"
            value={typeof condition.value === "string" ? condition.value : ''}
            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
            onClick={(e) => {
              e.stopPropagation();
              setIsDatePickerActive(true);
            }}
            onFocus={(e) => {
              e.stopPropagation();
              setIsDatePickerActive(true);
            }}
            onBlur={(e) => {
              e.stopPropagation();
              // Delay reset to give native date picker time to complete
              setTimeout(() => setIsDatePickerActive(false), 300);
            }}
            onMouseDown={(e) => e.stopPropagation()}
            className="w-full h-9"
          />
        );

      case 'range':
        // Type guard to ensure we have a range object
        const isRangeValue = (value: any): value is { from?: string; to?: string } => {
          return typeof value === "object" && value !== null && !Array.isArray(value) && !(value instanceof Date);
        };

        const rangeValue = isRangeValue(condition.value) ? condition.value : { from: '', to: '' };
        return (
          <div className="flex gap-2 items-center">
            <Input
              type="number"
              placeholder="Min"
              value={rangeValue.from || ''}
              onChange={(e) => updateCondition(condition.id, {
                value: { ...rangeValue, from: e.target.value }
              })}
              className="flex-1 h-9"
            />
            <span className="text-gray-400">to</span>
            <Input
              type="number"
              placeholder="Max"
              value={rangeValue.to || ''}
              onChange={(e) => updateCondition(condition.id, {
                value: { ...rangeValue, to: e.target.value }
              })}
              className="flex-1 h-9"
            />
          </div>
        );

      case 'exact':
      case 'contains':
      case 'starts_with':
      case 'ends_with':
      default:
        return (
          <Input
            placeholder={`Enter ${field.displayName.toLowerCase()}`}
            value={typeof condition.value === "string" ? condition.value : ''}
            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
            className="w-full h-9"
            showClearButton={true}
            onClear={() => updateCondition(condition.id, { value: '' })}
          />
        );
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        // Prevent closing during date picker interactions
        if (!open) {
          if (preventClose || isDatePickerActive) {
            console.log('[AdvancedSearch] Prevent close due to active date interaction', {
              preventClose,
              isDatePickerActive
            });
            return;
          }
        }
        setIsOpen(open);
      }}
    >
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 text-xs">
          <Filter className="mr-1 h-3 w-3" />
          Advanced Search
          {currentConditions.length > 0 && (
            <Badge variant="secondary" className="ml-2 h-4 text-xs">
              {currentConditions.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent
        className="max-w-4xl max-h-[80vh] overflow-y-auto"
        onInteractOutside={(e) => {
          // Prevent default onInteractOutside behavior
          // We handle this with our custom click outside detection
          e.preventDefault();
        }}
        onClickCapture={(e) => {
          // 只在必要时阻止点击事件
          if (preventClose || isDatePickerActive) {
            const target = e.target as HTMLElement;
            // 只有当点击不在日期相关元素上时才阻止
            if (!target.closest('[data-date-picker-panel]') &&
                !target.closest('input[type="date"]') &&
                !target.closest('.lucide-calendar')) {
              console.log('[AdvancedSearch] Blocking click during active date interaction');
              e.stopPropagation();
            }
          }
        }}
        onEscapeKeyDown={(e) => {
          // Prevent ESC during date interactions
          if (preventClose || isDatePickerActive) {
            console.log('[AdvancedSearch] Prevent close via ESC due to active date interaction');
            e.preventDefault();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Search
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Filter Integration Section */}
          {showFilterIntegration && hasActiveFilters && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ArrowDown className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    Import Current Filters
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleImportFilters}
                  className="text-blue-700 border-blue-300 hover:bg-blue-100"
                >
                  Add Filters
                </Button>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                Populate search conditions from your current filter selections
              </p>
            </div>
          )}

          {/* Search Conditions */}
          {conditions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No search conditions added yet.</p>
              <p className="text-sm">Click "Add Condition" to start building your search.</p>
              {hasActiveFilters && (
                <p className="text-sm text-blue-600 mt-2">
                  Or use the "Add Filters" button above to populate from current filters.
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {conditions.map((condition, index) => {
                const field = searchableFields.find(f => f.fieldName === condition.field);
                if (!field) return null;

                return (
                  <div key={condition.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center gap-3 mb-3">
                      {index > 0 && (
                        <Select
                          value={condition.logic || 'AND'}
                          onValueChange={(value) => updateCondition(condition.id, {
                            logic: value as 'AND' | 'OR'
                          })}
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="AND">AND</SelectItem>
                            <SelectItem value="OR">OR</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCondition(condition.id)}
                        className="ml-auto text-red-600 hover:text-red-800 hover:bg-red-50"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex flex-col lg:flex-row lg:items-start gap-3">
                      {/* Field Selection */}
                      <div className="w-full lg:w-48">
                        <label className="block text-sm font-medium mb-0.5">Field</label>
                        <Select
                          value={condition.field}
                          onValueChange={(value) => updateCondition(condition.id, {
                            field: value,
                            value: '' // Reset value when field changes
                          })}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {searchableFields.map(field => (
                              <SelectItem key={field.fieldName} value={field.fieldName}>
                                {field.displayName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Value Input */}
                      <div className="w-full lg:w-48">
                        <label className="block text-sm font-medium mb-0.5">Value</label>
                        {renderValueInput(condition, field)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <Button
              variant="outline"
              onClick={addCondition}
              className="flex items-center gap-2"
              disabled={searchableFields.length === 0}
            >
              <Plus className="h-4 w-4" />
              Add Condition
            </Button>

            <div className="flex gap-2">
              <Button variant="ghost" onClick={handleClear}>
                Clear All
              </Button>
              <Button
                onClick={handleSearch}
                disabled={conditions.length === 0}
              >
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
